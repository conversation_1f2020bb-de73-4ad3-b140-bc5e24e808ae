<?php require_once('header.php'); ?>

  <!-- Full Width Column -->
  <div class="content-wrapper">
    <div class="container">
      <!-- Content Header (Page header) -->
      <section class="content-header">
        <h1>
          Daftar Hadir Pelatihan Asesor BAN PDM Prov. Kaltim
        </h1>
      </section>

      <!-- Main content -->
      <section class="content">
        <div class="box box-default">
          <div class="box-header with-border">
            <h3 class="box-title">Form Pengisian Daftar Hadir Pelatihan Asesor BAN PDM Prov. Kaltim</h3> <br> <br> <br>
            <form action="absensi_pelatihan_asesor_create.php" method="post" name="smoothed" id="smoothed" >
                <input name="absensi_id" id="absensi_id" type="hidden" value=""/>
                <input name="absensi_kode" id="absensi_kode" type="hidden" value="<?php echo uniqid(uniqid(uniqid())); ?>" />
                <input name="provinsi_id" id="provinsi_id" type="hidden" value="64" />
              
              <?php
              // Array jadwal sesi pelatihan
              $jadwal = array(
                  array('tanggal' => '29-08-2025', 'mulai' => '08:00', 'selesai' => '08:15', 'value' => '1. Pembukaan'),
                  array('tanggal' => '11-08-2025', 'mulai' => '08:15', 'selesai' => '10:45', 'value' => '2. Modul 1: Paradigma dan Kebijakan Akreditasi Terkini (Mendengarkan paparan)'),
                  array('tanggal' => '29-08-2025', 'mulai' => '13:00', 'selesai' => '13:45', 'value' => '3. Modul 2: Etika, Profesionalisme dan Peran Asesor (Mendengarkan paparan dan diskusi hasil studi kasus.)'),
                  array('tanggal' => '30-08-2025', 'mulai' => '08:00', 'selesai' => '08:45', 'value' => '4. Modul 3: Eksplorasi Mendalam Instrumen Akreditasi (Diskusi gambaran umum Instrumen Akreditasi 2024)'),
                  array('tanggal' => '30-08-2025', 'mulai' => '10:15', 'selesai' => '11:45', 'value' => '5. Modul 3: Eksplorasi Mendalam Instrumen Akreditasi (Diskusi Simulasi Butir 1)'),
                  array('tanggal' => '30-08-2025', 'mulai' => '14:15', 'selesai' => '15:00', 'value' => '6. Modul 3: Eksplorasi Mendalam Instrumen Akreditasi (Diskusi Simulasi Butir 2)'),
                  array('tanggal' => '01-09-2025', 'mulai' => '09:30', 'selesai' => '10:15', 'value' => '7. Modul 3: Eksplorasi Mendalam Instrumen Akreditasi (Diskusi Simulasi Butir 5)'),
                  array('tanggal' => '01-09-2025', 'mulai' => '12:45', 'selesai' => '13:30', 'value' => '8. Modul 3: Eksplorasi Mendalam Instrumen Akreditasi (Diskusi Simulasi Butir 10)'),
                  array('tanggal' => '01-09-2025', 'mulai' => '14:15', 'selesai' => '15:00', 'value' => '9. Modul 4: Teknik Penggalian Data (Diskusi penggalian data dengan konteks Butir 3)'),
                  array('tanggal' => '02-09-2025', 'mulai' => '10:15', 'selesai' => '11:45', 'value' => '10. Modul 5: Analisis Data dan Validasi Data (Paparan dan Diskusi tentang Inkonsistensi Data dan Professional Judgement)'),
                  array('tanggal' => '02-09-2025', 'mulai' => '14:15', 'selesai' => '15:00', 'value' => '11. Modul 5: Analisis Data dan Validasi Data (Paparan dan Diskusi tentang Penyusunan Rasionalisasi Penilaian)'),
                  array('tanggal' => '03-09-2025', 'mulai' => '13:30', 'selesai' => '15:00', 'value' => '12. Modul 6: Penyusunan Catatan dan Saran untuk Perbaikan Berkelanjutan (Penyusunan saran dan Rekomendasi)'),
                  array('tanggal' => '03-09-2025', 'mulai' => '15:00', 'selesai' => '16:30', 'value' => '13. Modul 7: Sispena (Pemaparan tentang Sispena)'),
                  array('tanggal' => '03-09-2025', 'mulai' => '16:30', 'selesai' => '17:00', 'value' => '14. Penutupan')
              );

              // Waktu sekarang
              $tgl_sekarang = date('d-m-Y');
              $jam_sekarang = date('H:i');

              // Fungsi untuk mengecek apakah sesi aktif
              function isSesiAktif($sesi, $tgl_sekarang, $jam_sekarang) {
                  return ($sesi['tanggal'] == $tgl_sekarang) && 
                         ($jam_sekarang >= $sesi['mulai'] && $jam_sekarang <= $sesi['selesai']);
              }
              ?>

              <style>
                /* Style untuk radio button aktif dan tidak aktif */
                .radio-aktif {
                  color: #333;
                  font-weight: normal;
                }
                .radio-tidak-aktif {
                  color: #999;
                  font-style: italic;
                }
                .radio-aktif label {
                  cursor: pointer;
                }
                .radio-tidak-aktif label {
                  cursor: not-allowed;
                }
                .sedang-berlangsung {
                  color: #28a745;
                  font-weight: bold;
                }
              </style>

              <table align="center" width="10%" border="0" style="font-family:Arial, Helvetica, sans-serif; font-size:13px">
                <tr>
                  <td><strong>NAMA</strong></td>
                </tr>
                <tr>
                  <td>
                      <input type="text" class="form-control" name="nama" id="nama" value="" required="" />
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td><strong>NIA <i>(Diisi jika peserta dari unsur asesor)</i></strong></td>
                </tr>
                <tr>
                  <td>
                      <input type="text" class="form-control" name="nia" id="nia" value="" />
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td><strong>ASAL KABUPATEN / KOTA</strong></td>
                </tr>
                <tr>
                  <td>
                      <input type="text" class="form-control" name="kab_kota" id="kab_kota" value="" required="" />
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td><strong>ASAL PROVINSI</strong></td>
                </tr>
                <tr>
                  <td>
                      <input type="text" class="form-control" name="provinsi" id="provinsi" value="" required="" />
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td><strong>SEBAGAI</strong></td>
                </tr>
                <tr>
                  <td>
                      <select name="sebagai_id" id="sebagai_id" class="form-control select2" style="width: 100%;" required="">
                      <option selected="selected" value="">Silahkan Pilih</option>
                        <?php
                          $sql_sebagai = "SELECT * FROM absensi_pelatihan_asesor_sebagai ORDER BY no_urut ASC";
                          $result_sebagai = $connect->query($sql_sebagai);
                          while($row_sebagai = $result_sebagai->fetch_assoc()) {
                          echo "<option value='".$row_sebagai['sebagai_id']."'> ".$row_sebagai['nm_sebagai']."</option>";
                          }
                        ?>
                      </select>
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td><strong>JABATAN</strong></td>
                </tr>
                <tr>
                  <td>
                      <select name="jabatan_id" id="jabatan_id" class="form-control select2" style="width: 100%;" required="">
                      <option selected="selected" value="">Silahkan Pilih</option>
                        <?php
                          $sql_jabatan = "SELECT * FROM absensi_pelatihan_asesor_jabatan ORDER BY no_urut ASC";
                          $result_jabatan = $connect->query($sql_jabatan);
                          while($row_jabatan = $result_jabatan->fetch_assoc()) {
                          echo "<option value='".$row_jabatan['jabatan_id']."'> ".$row_jabatan['nm_jabatan']."</option>";
                          }
                        ?>
                      </select>
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td><strong>KELAS</strong></td>
                </tr>
                <tr>
                  <td>
                      <select name="kelas" id="kelas" class="form-control"  required="">
                        <option selected="selected" value="">Silahkan Pilih</option>
                        <option id="kelas" value="A">A</option>
                        <option id="kelas" value="B">B</option>
                        <option id="kelas" value="C">C</option>
                        <option id="kelas" value="D">D</option>
                      </select>
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td><strong>MATERI PELATIHAN</strong></td>
                </tr>
                <tr>
                  <td>
                      <?php foreach ($jadwal as $index => $sesi): 
                        $is_active = isSesiAktif($sesi, $tgl_sekarang, $jam_sekarang);
                      ?>
                      <div class="radio <?php echo $is_active ? 'radio-aktif' : 'radio-tidak-aktif'; ?>">
                        <label>
                          <input type="radio" name="sesi" id="sesi_<?php echo $index; ?>" value="<?php echo $sesi['value']; ?>" 
                              <?php echo $is_active ? '' : 'disabled'; ?>>
                          <?php echo $sesi['value']; ?>
                          <?php if ($is_active): ?>
                              <span class="sedang-berlangsung"> (SEDANG BERLANGSUNG)</span>
                          <?php endif; ?>
                        </label>
                      </div>
                      <?php endforeach; ?>
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td><strong>TANDA TANGAN</strong></td>
                </tr>
                <tr>
                  <td style="background-color:#CCC">
                    <canvas class="pad" width="300" height="150"></canvas>
                    <input type="hidden" name="output" class="output" required>
                      <fieldset>
                        <input type="reset" value="Ulangi jika salah" />
                      </fieldset>   
                  </td>
                </tr>
                <tr><td>&nbsp;</td></tr>
                <tr>
                  <td>
                    <input name="simpan" type="submit" value="SIMPAN" class="btn btn-primary">
                  </td>
                </tr>
              </table>
            </form>
          </div>
          <div class="box-body table-responsive"> <h4><b>List Daftar Hadir Pelatihan Asesor BAN PDM Prov. Kaltim</b></h4>
            <table class="table table-bordered table-hover">
              <thead>
              <tr>
                <th bgcolor="#cccccc">NO <br> &nbsp;</th>
                <th bgcolor="#cccccc">NAMA <br> &nbsp;</th>
                <th bgcolor="#cccccc">NIA <br> &nbsp;</th>
                <th bgcolor="#cccccc">KABUPATEN <br> KOTA</th>
                <th bgcolor="#cccccc">PROVINSI <br> &nbsp;</th>
                <th bgcolor="#cccccc">SEBAGAI <br> &nbsp;</th>
                <th bgcolor="#cccccc">JABATAN <br> &nbsp;</th>
                <th bgcolor="#cccccc">KELAS <br> &nbsp;</th>
                <th bgcolor="#cccccc">MATERI <br> PELATIHAN</th>
                <th bgcolor="#cccccc">TANGGAL <br> &nbsp;</th>
                <th bgcolor="#cccccc">JAM <br> &nbsp;</th>
                <th bgcolor="#cccccc">TANDA <br> TANGAN</th>
              </tr>
              </thead>
              <tbody>
                <?php
                  $sql = "SELECT absensi_id, nama, nia, kab_kota, provinsi, 
                          absensi_pelatihan_asesor_sebagai.nm_sebagai, 
                          absensi_pelatihan_asesor_jabatan.nm_jabatan, kelas, sesi, tanggal, jam, output FROM absensi_pelatihan_asesor
                          JOIN absensi_pelatihan_asesor_sebagai ON absensi_pelatihan_asesor.sebagai_id=
                          absensi_pelatihan_asesor_sebagai.sebagai_id
                          JOIN absensi_pelatihan_asesor_jabatan ON absensi_pelatihan_asesor.jabatan_id=
                          absensi_pelatihan_asesor_jabatan.jabatan_id
                          WHERE tanggal='$tglsekarang' AND absensi_pelatihan_asesor.provinsi_id='64' ORDER BY kelas ASC, nama ASC, sesi ASC";
                  $result = $connect->query($sql);
                  if($result->num_rows > 0) {
                  while($row = $result->fetch_assoc()) {
                  $nomor++;
                ?>
              <tr>
                <td><?php echo $nomor; ?></td>
                <td><?php echo $row['nama']; ?></td>
                <td><?php echo $row['nia']; ?></td>
                <td><?php echo $row['kab_kota']; ?></td>
                <td><?php echo $row['provinsi']; ?></td>
                <td><?php echo $row['nm_sebagai']; ?></td>
                <td><?php echo $row['nm_jabatan']; ?></td>
                <td><?php echo $row['kelas']; ?></td>
                <td><?php echo $row['sesi']; ?></td>
                <td><?php echo format_tgl_saja($row['tanggal']); ?></td>
                <td><?php echo $row['jam']; ?></td>
                <td><img class="img-fluid" src="../../../files/ttd_image_pelatihan_asesor/<?php echo $row['output']; ?>" alt="" height="50" ></td>
              </tr>
              <?php } } ?>
              </tbody>
            </table>
          </div>
          <!-- /.box-body -->
        </div>
        <!-- /.box -->
      </section>
      <!-- /.content -->
    </div>
    <!-- /.container -->
  </div>
  <!-- /.content-wrapper -->

<?php require_once('footer.php'); ?>